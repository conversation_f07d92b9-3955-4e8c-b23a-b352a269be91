import { refreshToken } from './refreshToken';
import { store } from '@/stores';

/**
 * 验证token是否有效，如果无效则尝试刷新
 * @returns Promise<boolean> 返回token是否有效
 */
export const validateAndRefreshToken = async (): Promise<boolean> => {
  try {
    const token = localStorage.getItem('token');
    const refreshTokenValue = localStorage.getItem('refresh_token') || localStorage.getItem('refreshToken');
    
    // 如果没有token，返回false
    if (!token || !refreshTokenValue) {
      return false;
    }
    
    // 检查token是否过期（简单的JWT解析）
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      
      // 如果token还有效（还有5分钟以上的有效期），直接返回true
      if (payload.exp && payload.exp > currentTime + 300) {
        return true;
      }
      
      // 如果token即将过期或已过期，尝试刷新
      console.log('Token即将过期或已过期，尝试刷新...');
      const newToken = await refreshToken();
      
      if (newToken) {
        console.log('Token刷新成功');
        return true;
      } else {
        console.log('Token刷新失败');
        return false;
      }
    } catch (parseError) {
      // 如果无法解析token，尝试刷新
      console.log('无法解析token，尝试刷新...');
      const newToken = await refreshToken();
      return !!newToken;
    }
  } catch (error) {
    console.error('验证token时发生错误:', error);
    return false;
  }
};

/**
 * 检查用户是否已认证
 * @returns boolean
 */
export const isUserAuthenticated = (): boolean => {
  const state = store.getState();
  const token = localStorage.getItem('token');
  
  return !!(state.user.isAuthenticated && state.user.token && token);
};

/**
 * 清除所有认证相关的数据
 */
export const clearAuthData = (): void => {
  // 清除localStorage
  localStorage.removeItem('token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('user');
  localStorage.removeItem('userInfo');
  localStorage.removeItem('persist:root');
  
  // 清除sessionStorage
  sessionStorage.removeItem('token');
  sessionStorage.removeItem('refresh_token');
  sessionStorage.removeItem('refreshToken');
  
  // 清除Redux状态
  store.dispatch({ type: 'user/clearUserInfo' });
};
