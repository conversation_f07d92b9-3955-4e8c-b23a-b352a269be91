import {
  fetchBase<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FetchBaseQueryError,
  BaseQueryApi,
  createApi
} from '@reduxjs/toolkit/query/react';
import { RootState } from '..';
import { refreshToken } from '@/utils/refreshToken';
import { clearUserInfo } from '@/stores/slices/user';
import { store } from '@/stores';
import { customToast } from '@/lib/toast';

// 定义通用响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 定义分页响应类型
export interface PaginatedResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 创建一个错误计数器对象
const errorCounter = {
  count: 0,
  maxRetries: 3,
  reset() {
    this.count = 0;
  },
  increment() {
    this.count += 1;
    return this.count;
  },
  exceedsMaxRetries() {
    return this.count >= this.maxRetries;
  }
};

// 创建基础查询配置
const baseQuery = fetchBaseQuery({
  baseUrl: `${process.env.NEXT_PUBLIC_API_URL}`,
  prepareHeaders: (headers, { getState }) => {
    const token = (getState() as RootState).user?.token;
    if (token) {
      headers.set('authorization', `Bearer ${token}`);
    }
    return headers;
  }
});

// 创建一个带有重试逻辑的 baseQuery
export const baseQueryWithReauth = async (
  args: string | FetchArgs,
  api: BaseQueryApi,
  extraOptions: { [key: string]: any }
) => {
  // 执行原始请求
  let result = await baseQuery(args, api, extraOptions);
  console.log(result, "result base api.");
  // 检查是否是 401 错误（未授权）
  if (result.error) {
    const status = result.error.status;
    const errorData = result.error.data as { code?: number } | undefined;

    if (status === 401 || errorData?.code === 401) {
      console.log('Token 已过期，尝试刷新...');

      // 增加错误计数
      const currentErrorCount = errorCounter.increment();
      console.log(`Token 错误计数: ${currentErrorCount}/${errorCounter.maxRetries}`);

      // 如果超过最大重试次数，则退出登录
      if (errorCounter.exceedsMaxRetries()) {
        console.log('Token 错误次数超过最大限制，退出登录');

        // 显示友好的错误提示
        customToast.error('登录已过期', '请重新登录');

        // 清除用户信息
        store.dispatch(clearUserInfo());

        // 清除本地存储的令牌
        localStorage.removeItem('token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
        localStorage.removeItem('persist:root');

        // 只有在不是登录页面时才重定向
        if (window.location.pathname !== '/login') {
          // 延迟重定向到登录页，给用户时间看到提示
          setTimeout(() => {
            window.location.href = '/login';
          }, 1500);
        }

        return result;
      }

      // 尝试刷新 token
      try {
        const newToken = await refreshToken();

        // 如果成功获取新 token，重试原始请求
        if (newToken) {
          console.log('Token 刷新成功，重试请求');
          // 重置错误计数
          errorCounter.reset();
          // 使用新 token 重试请求
          result = await baseQuery(args, api, extraOptions);
        } else {
          // 如果刷新失败但没有抛出异常（返回null）
          console.error('Token 刷新失败，无法获取新token');

          // 增加错误计数，但不立即跳转
          const currentErrorCount = errorCounter.increment();

          if (errorCounter.exceedsMaxRetries()) {
            customToast.error('登录已过期', '请重新登录');

            // 清除用户信息
            store.dispatch(clearUserInfo());

            // 清除本地存储
            localStorage.removeItem('token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('user');
            localStorage.removeItem('persist:root');

            // 只有在不是登录页面时才重定向
            if (window.location.pathname !== '/login') {
              setTimeout(() => {
                window.location.href = '/login';
              }, 1500);
            }
          }
        }
      } catch (error) {
        console.error('Token 刷新过程中发生错误:', error);

        // 增加错误计数
        const currentErrorCount = errorCounter.increment();

        if (errorCounter.exceedsMaxRetries()) {
          customToast.error('登录已过期', '请重新登录');

          // 清除用户信息
          store.dispatch(clearUserInfo());

          // 清除本地存储
          localStorage.removeItem('token');
          localStorage.removeItem('refresh_token');
          localStorage.removeItem('refreshToken');
          localStorage.removeItem('user');
          localStorage.removeItem('persist:root');

          // 只有在不是登录页面时才重定向
          if (window.location.pathname !== '/login') {
            setTimeout(() => {
              window.location.href = '/login';
            }, 1500);
          }
        }
      }
    } else {
      // 如果不是 401 错误，重置错误计数
      errorCounter.reset();
    }
  } else {
    // 请求成功，重置错误计数
    errorCounter.reset();
  }

  return result;
};

// 创建基础API配置
export const createBaseApi = (name: string, tagTypes: string[] = []) => {
  return createApi({
    reducerPath: name,
    keepUnusedDataFor: 60, // 未使用数据 60 秒后自动清理
    baseQuery: baseQueryWithReauth,
    tagTypes,
    endpoints: () => ({})
  });
};
