'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useEffect, useState } from "react";
import { customToast } from '@/lib/toast';
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Upload } from 'lucide-react';
import { useGetStaffRoleListQuery, useUploadStaffAvatarMutation } from "@/stores/services/staffApi";

export interface StaffFormData {
  name: string;
  phone: string;
  account: string;
  roles: string[];
  description: string;
  attendanceType: 'fixed' | 'flexible';
  avatar: string;
  isShow: boolean;
}

interface StaffDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  staff?: any;
  mode: 'create' | 'edit';
  onSubmit: (formData: StaffFormData) => void;
  loading?: boolean;
}

export default function StaffDialog({
  open,
  onOpenChange,
  staff,
  mode = 'edit',
  onSubmit,
  loading = false,
}: StaffDialogProps) {
  const { data: rolesData } = useGetStaffRoleListQuery({});
  const [uploadStaffAvatarMutation] = useUploadStaffAvatarMutation();

  const [formData, setFormData] = useState<StaffFormData>({
    name: '',
    phone: '',
    account: '',
    roles: [],
    description: '',
    attendanceType: 'fixed',
    avatar: '',
    isShow: false
  });
  
  useEffect(() => {
    if (mode === 'edit' && staff) {
      setFormData({
        name: staff.name || '',
        phone: staff.phone || '',
        account: staff.account || '',
        roles: staff.userRoles?.map(({ role }: { role: any }) => role.id) || [],
        description: staff.description || '',
        attendanceType: staff.attendanceType || 'fixed',
        avatar: staff.avatar || '',
        isShow: staff.isShow ?? true
      });
    } else {
      // Reset form for create mode or when dialog reopens
      setFormData({
        name: '',
        phone: '',
        account: '',
        roles: [],
        description: '',
        attendanceType: 'fixed',
        avatar: '',
        isShow: false
      });
    }
  }, [staff, mode, open]);

  const validateForm = () => {
    if (!formData.name.trim()) {
      customToast.warning('请填写姓名');
      return false;
    }
    if (!formData.phone.trim()) {
      customToast.warning('请填写手机号码');
      return false;
    }
    if (!formData.account.trim()) {
      customToast.warning('请填写登录账号');
      return false;
    }
    if (formData.roles.length === 0) {
      customToast.warning('请选择岗位');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    onSubmit(formData);
  };

  const handleAvatarUpload = async (e: any) => {
    const input = e.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) {
      customToast.warning('请选择图片文件');
      return;
    }
    const file = input.files[0];
    if (!(file instanceof File)) {
      customToast.error('文件无效');
      return;
    }

    const formDataObj = new FormData();
    formDataObj.append('file', file);

    try {
      const res = await uploadStaffAvatarMutation(formDataObj);
      const avatarUrl = res?.data?.data || '';

      if (avatarUrl) {
        setFormData({ ...formData, avatar: avatarUrl });
        customToast.success('头像上传成功');
      } else {
        customToast.error('头像上传失败');
      }
    } catch (err) {
      console.error('头像上传错误:', err);
      customToast.error('头像上传失败');
    }
  }

  // 确保 roles 是数组
  const roles = Array.isArray(rolesData?.list) ? rolesData?.list : [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] w-[95vw] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-3">
          <DialogTitle className="text-xl font-semibold">
            {mode === 'edit' ? '编辑员工信息' : '新增员工'}
          </DialogTitle>
          <DialogDescription className="text-muted-foreground">
            请填写员工信息，带 <span className="text-red-500">*</span> 为必填项
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          <div className="flex items-center gap-6">
            <div className="relative group cursor-pointer">
              <Avatar className="h-20 w-20" >
                <AvatarImage
                  src={formData.avatar} />
                <AvatarFallback className="text-lg">{formData.name?.[0]?.toUpperCase()}</AvatarFallback>
              </Avatar>
              <div className="absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-full">
                <Upload className="h-6 w-6 text-white" />
              </div>
              <input
                type="file"
                className="absolute inset-0 opacity-0 cursor-pointer"
                accept="image/*"
                onChange={handleAvatarUpload}
              />
            </div>
            <div className="grid grid-cols-2 gap-6 flex-1">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium">
                  姓名 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="请输入姓名"
                  className="w-full"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone" className="text-sm font-medium">
                  手机号码 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  placeholder="请输入手机号码"
                  className="w-full"
                />
              </div>
            </div>
          </div>
          <div className="grid grid-cols-4 gap-6">
            <div className="space-y-2">
              <Label htmlFor="account" className="text-sm font-medium">
                登录账号 <span className="text-red-500">*</span>
              </Label>
              <Input
                id="account"
                value={formData.account}
                onChange={(e) => setFormData({ ...formData, account: e.target.value })}
                placeholder="请输入登录账号"
                className="w-full"
                disabled={mode === 'edit'} // 编辑模式下不允许修改账号
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="roles" className="text-sm font-medium">
                岗位 <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.roles.join(',')}
                onValueChange={(value) => setFormData({ ...formData, roles: value ? value.split(',') : [] })}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="请选择岗位" />
                </SelectTrigger>
                <SelectContent>
                  {roles?.map((role: any) => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="attendanceType" className="text-sm font-medium">考勤方式</Label>
              <Select
                value={formData.attendanceType}
                onValueChange={(value: 'fixed' | 'flexible') => setFormData({ ...formData, attendanceType: value })}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="请选择考勤方式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fixed">固定班制</SelectItem>
                  <SelectItem value="flexible">弹性工时</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="isShow" className="text-sm font-medium">是否展示</Label>
              <Select
                value={formData.isShow.toString()}
                onValueChange={(value) => setFormData({ ...formData, isShow: value === 'true' })}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="请选择是否展示" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">是</SelectItem>
                  <SelectItem value="false">否</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium">员工简介</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="请输入员工简介"
              className="w-full min-h-[100px]"
            />
          </div>
        </div>
        <DialogFooter className="gap-2 sm:gap-0">
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
            取消
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? '处理中...' : '确定'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
