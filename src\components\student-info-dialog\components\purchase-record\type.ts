export interface PurchaseRecordTableType {
  // 新
  id: string;
  amount: number;
  amountPaid: number;
  amountUnpaid: number;
  paymentTime: string;
  paymentMethod: string;
  discount: number;
  giftCount: number;
  giftDays: number;
  purchaseQuantity: number;
  studentProductPaymentStatus: string;
  createdAt: string
  productId: string
  productName: string
  productPackageType: string
  operatorName: string
  salesRepName: string
  status: string
  studentProductEndDate: number
  studentProductEnrollmentStatus: string
  studentProductId: string
  studentProductPaymentTime: string
  studentProductRemainingSessionCount: number
  studentProductStartDate: number
  studentProductTotalSessionCount: number
  studentProductRemainingBalance: number
  updatedAt: string
}
