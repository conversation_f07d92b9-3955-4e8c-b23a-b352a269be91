'use client';

import { useAppDispatch, useAppSelector } from "@/stores/hooks";
import { useGetUserMenusQuery } from "@/stores/services/menusApi";
import { useGetCurrentPermissionsQuery } from "@/stores/services/usersApi";
import { usePathname, useRouter } from "next/navigation";
import { createContext, useContext, useEffect, useState, useMemo, useCallback } from "react";
import { setPermissions } from "@/stores/slices/user-permissions";
import { validateAndRefreshToken, clearAuthData } from "@/utils/tokenValidator";

// 定义类型
interface MenuItem {
  id: string;
  name: string;
  path: string;
  parentId: string | null;
  sort: number;
  component: string;
  icon: string;
  hidden: boolean;
  permissionId: string | null;
  children?: MenuItem[];
}

interface AuthContextType {
  menus: MenuItem[];
  loading: boolean;
  logout: () => void;
  checkAccess: (path: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 公共路径列表 - 提取为常量
const PUBLIC_PATHS = ['/', '/login', '/404', '/unauthorized','/1', '/notifications/list', '/features', '/features/course-scheduling'];

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const user = useAppSelector(state => state.user);
  const [menus, setMenus] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);
  const [authChecked, setAuthChecked] = useState(false); // 新增：标记是否已检查认证状态
  const dispatch = useAppDispatch();
  const router = useRouter();
  const pathname = usePathname();
  const userMenus = useAppSelector(state => state.userMenus.menus);
  const userPermissions = useAppSelector(state => state.userPermissions.permissions);
  console.log(userPermissions,"userPermissions")

  // 获取用户菜单
  const { data: menuData, isLoading: isMenuLoading } = useGetUserMenusQuery({}, {
    skip: !user.isAuthenticated || !user.token || (userMenus?.length || 0) > 0
  });

  // 获取用户权限
  const { data: permissionsData, isLoading: isPermissionsLoading } = useGetCurrentPermissionsQuery({}, {
    skip: !user.isAuthenticated || !user.token || (userPermissions?.length || 0) > 0
  });

  // 优化的路径匹配函数
  const isPathMatch = useCallback((menuPath: string | undefined, currentPath: string): boolean => {
    // 处理未定义路径
    if (!menuPath) return false;

    // 精确匹配
    if (menuPath === currentPath) return true;

    // 获取路径段
    const menuSegments = menuPath.split('/').filter(Boolean);
    const currentSegments = currentPath.split('/').filter(Boolean);

    // 1. 段数检查 - 如果相差不超过1个段
    if (Math.abs(menuSegments.length - currentSegments.length) <= 1) {
      // 当前路径比菜单路径多一段 - 可能是ID在任何位置
      if (currentSegments.length === menuSegments.length + 1) {
        for (let i = 0; i < currentSegments.length; i++) {
          const testSegments = [...currentSegments];
          testSegments.splice(i, 1); // 移除可能的ID段

          if (menuSegments.join('/') === testSegments.join('/')) {
            return true;
          }
        }
      }

      // 段数相同但存在不同段 - 检查是否只有一个段不同
      if (currentSegments.length === menuSegments.length && currentSegments.length > 1) {
        let diffCount = 0;
        let matchCount = 0;

        for (let i = 0; i < menuSegments.length; i++) {
          if (menuSegments[i] !== currentSegments[i]) {
            diffCount++;
          } else {
            matchCount++;
          }
        }

        // 如果只有一个段不同，且至少有一个段匹配
        if (diffCount === 1 && matchCount > 0) {
          return true;
        }
      }
    }

    // 2. 标准化路径 - 移除数字ID
    const normalizedMenuPath = menuPath.replace(/\/\d+(?=\/|$)/g, '');
    const normalizedCurrentPath = currentPath.replace(/\/\d+(?=\/|$)/g, '');
    if (normalizedMenuPath === normalizedCurrentPath) return true;

    // 3. 处理动态路由参数
    if (menuPath.includes('[')) {
      try {
        const patternStr = menuPath
          .replace(/\/\[\.\.\..*?\]/g, '/(.+)')  // [...slug] => 多段路径
          .replace(/\/\[.*?\]/g, '/([^/]+)');    // [id] => 单段路径
        const regexPattern = new RegExp(`^${patternStr}$`);
        return regexPattern.test(currentPath);
      } catch (error) {
        console.error('路径匹配正则表达式错误:', error);
      }
    }

    return false;
  }, []);

  // 检查路径是否匹配菜单项数组中的任意项（包括子菜单）
  const hasMenuAccess = useCallback((menuItems: MenuItem[], path: string): boolean => {
    // 遍历菜单项
    for (const menu of menuItems) {
      // 检查当前菜单项
      if (isPathMatch(menu.path, path)) {
        return true;
      }

      // 检查子菜单项（如果有）
      if (menu.children && menu.children.length > 0) {
        if (hasMenuAccess(menu.children, path)) {
          return true;
        }
      }
    }

    return false;
  }, [isPathMatch]);

  // 使用useMemo缓存checkAccess函数
  const checkAccess = useMemo(() => {
    return (path: string): boolean => {
      // 公共路径无需权限检查
      if (PUBLIC_PATHS.includes(path)) return true;

      // 未登录用户无权限
      if (!user.isAuthenticated) return false;

      // 检查路径是否匹配任意菜单项
      return hasMenuAccess(menus, path);
    };
  }, [user.isAuthenticated, menus, hasMenuAccess]);

  // 初始化认证状态检查
  useEffect(() => {
    const initializeAuth = async () => {
      // 如果已经检查过认证状态，跳过
      if (authChecked) return;

      try {
        // 验证并刷新token
        const isTokenValid = await validateAndRefreshToken();

        if (isTokenValid) {
          // 如果token有效但Redux状态显示未认证，恢复用户状态
          if (!user.isAuthenticated) {
            const userInfo = localStorage.getItem('user');
            const token = localStorage.getItem('token');
            const refreshTokenValue = localStorage.getItem('refresh_token') || localStorage.getItem('refreshToken');

            if (userInfo && token && refreshTokenValue) {
              try {
                const parsedUser = JSON.parse(userInfo);
                dispatch({
                  type: 'user/setUserInfo',
                  payload: {
                    ...parsedUser,
                    token,
                    refresh_token: refreshTokenValue,
                    isAuthenticated: true
                  }
                });
                console.log('用户状态已恢复');
              } catch (parseError) {
                console.error('解析用户信息失败:', parseError);
                clearAuthData();
              }
            }
          }
        } else {
          // 如果token无效，清除认证数据
          console.log('Token验证失败，清除认证数据');
          clearAuthData();
        }
      } catch (error) {
        console.error('初始化认证状态时发生错误:', error);
        clearAuthData();
      } finally {
        setAuthChecked(true);
      }
    };

    initializeAuth();
  }, [user.isAuthenticated, dispatch, authChecked]);

  // 初始化加载菜单
  useEffect(() => {
    if (userMenus.length > 0) {
      setMenus(userMenus);
      setLoading(false);
      setInitialized(true);
    } else if (menuData) {
      setMenus(menuData);
      dispatch({ type: 'userMenus/setUserMenus', payload: menuData });
      setLoading(false);
      setInitialized(true);
    } else {
      setLoading(isMenuLoading);
    }
  }, [userMenus, menuData, isMenuLoading, dispatch]);

   // 处理权限数据
   console.log(permissionsData,"permissionsData")
  useEffect(() => {
    if (permissionsData) {
      dispatch(setPermissions({ permissions: permissionsData }))
    }
  }, [permissionsData]);

  // 路径变化时进行权限检查
  useEffect(() => {
    // 只在认证状态检查完成、初始化完成且不在加载状态时检查权限
    if (authChecked && initialized && !loading && pathname) {
      const hasAccess = checkAccess(pathname);

      // 只有当确认无权限时才重定向
      if (!hasAccess) {
        console.log("没有权限访问该页面", pathname);

        if (!user.isAuthenticated) {
          router.push(`/login?redirect=${encodeURIComponent(pathname)}`);
        } else {
          // 添加一个短延迟，避免在数据还未完全加载时就重定向
          const timeoutId = setTimeout(() => {
            // 再次检查权限，确保数据已完全加载
            const finalCheck = checkAccess(pathname);
            if (!finalCheck) {
              router.push('/404');
            }
          }, 100);

          return () => clearTimeout(timeoutId);
        }
      }
    }
  }, [pathname, loading, initialized, authChecked, user.isAuthenticated, router, checkAccess]);

  async function Logout() {
    const token = user.token || localStorage.getItem('token')
    console.log(token,"user token")
    await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/logout`, { // URL 作为第一个参数
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
  }


  // 退出登录处理函数
  const logout = useCallback(async () => {
    try {
      // 调用登出API
      await Logout();
    } catch (error) {
      console.error('登出API调用失败:', error);
    } finally {
      // 无论API调用成功与否，都清除本地状态
      // 清除Redux状态
      dispatch({ type: 'user/clearUserInfo' });
      dispatch({ type: 'userMenus/clearMenus' });
      dispatch({ type: 'userPermissions/clearPermissions' });

      // 清除所有localStorage中的认证相关数据
      localStorage.removeItem('userInfo');
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('refreshToken');

      // 清除sessionStorage中可能存在的数据
      sessionStorage.removeItem('token');
      sessionStorage.removeItem('refresh_token');
      sessionStorage.removeItem('refreshToken');

      // 清除Redux持久化数据
      localStorage.removeItem('persist:root');

      // 使用replace而不是push，防止用户通过浏览器返回按钮回到需要认证的页面
      router.replace('/login');
    }
  }, [dispatch, router]);

  return (
    <AuthContext.Provider value={{
      menus,
      loading,
      checkAccess,
      logout
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
