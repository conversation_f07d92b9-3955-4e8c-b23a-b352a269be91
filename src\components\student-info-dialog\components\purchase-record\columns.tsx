import React from "react";
import { ColumnDef } from "@tanstack/react-table";

import { PurchaseRecordTableType } from "./type";
import { Badge } from "@/components/ui/badge";
import { format, isValid } from "date-fns";
import { zhCN } from 'date-fns/locale';
import { PACKAGE_TYPE_MAP,PAYMENT_METHOD_MAP, PURCHASE_RECORD_TYPE_MAP } from "@/constants";
import { cn } from "@/lib/utils";
import { formatCurrency } from "@/utils/format/formatCurrency";
import dynamic from "next/dynamic";
import { useAppSelector } from "@/stores/hooks";
const ActionProductRefund = dynamic(() => import("../package-table/actions/refund"), { ssr: false });

// 安全格式化日期，处理无效日期情况
const formatSafeDate = (dateString: string | null | number): JSX.Element => {
  if (!dateString) return <span className="text-slate-400 text-sm italic">-</span>;
  
  try {
    const date = new Date(dateString);
    if (!isValid(date)) throw new Error("无效日期");
    
    return <div>{format(date, 'yyyy-MM-dd', { locale: zhCN })}</div>;
  } catch (error) {
    return (
      <>-</>
    );
  }
};



export const purchaseRecordTableColumns: ColumnDef<PurchaseRecordTableType>[] = [
  {
    accessorKey: "name",
    header: '商品名称',
    cell: ({ row }) => {
      const productName = row.original?.productName || "未命名商品";
      return <div className="font-medium text-slate-800">{productName}</div>;
    },
  },
  {
    accessorKey: "packageType",
    header: "套餐类型",
    cell: ({ row }) => {
      const packageType = row.original.productPackageType as keyof typeof PACKAGE_TYPE_MAP;
      const { color, label } = PACKAGE_TYPE_MAP[packageType] || PACKAGE_TYPE_MAP.default
      
      return (
        <Badge className={color}>{label}</Badge>
      )
    },
  },
  {
    accessorKey: "amount",
    header: "商品金额",
    cell: ({ row }) => {
      const amount = Number(row.getValue("amount") || 0);
      return (
        <div className="font-medium text-slate-800">
          {formatCurrency(amount)}
        </div>
      );
    },
  },
  {
    accessorKey: "amountPaid",
    header: "实收金额",
    cell: ({ row }) => {
      const amountPaid = Number(row.getValue("amountPaid") || 0);
      return (
        <div className="font-medium text-slate-800">
          {formatCurrency(amountPaid)}
        </div>
      );
    },
  },
  {
    accessorKey: "amountUnpaid",
    header: "欠款金额",
    cell: ({ row }) => {
      const amountUnpaid = Number(row.getValue("amountUnpaid") || 0);
      return (
        <div className="font-medium text-slate-800">
          {formatCurrency(amountUnpaid)}
        </div>
      );
    },
  },
  {
    accessorKey: "paymentMethod",
    header: "支付方式",
    cell: ({ row }) => {
      const payment = String(row.getValue("paymentMethod") || "other").toLowerCase();
      const { label, color } = PAYMENT_METHOD_MAP[payment] || PAYMENT_METHOD_MAP.other;
      
      return (
        <Badge className={cn("font-normal border py-0.5", color)}>
          {label}
        </Badge>
      );
    },
  },
  {
    accessorKey: "purchaseQuantity",
    header: "商品数量",
    cell: ({ row }) => {
      return (
        <div className="font-medium text-slate-800">
          {row.original.purchaseQuantity || 0}
        </div>
      );
    },
  },
  {
    accessorKey: "discount",
    header: "折扣",
    cell: ({ row }) => {
      const discount = Number(row.getValue("discount") || 0);
      return (
        <div className="text-slate-700">
          {discount === 10 || discount === 0 ? '无折扣' : `${discount} 折`}
        </div>
      );
    },
  },

  {
    accessorKey: "paymentTime",
    header: "支付时间",
    cell: ({ row }) => formatSafeDate(row.getValue("paymentTime")),
  },
  {
    accessorKey: "totalSessionCount",
    header: "购买次数/总",
    cell: ({ row }) => {
      const totalSessionCount = Number(row.original.studentProductTotalSessionCount || 0);
      return (
        <div className="text-center font-medium">
          {totalSessionCount > 0 ? totalSessionCount : '不限'}
        </div>
      );
    },
  },
  {
    accessorKey: "giftCount",
    header: "赠送次数",
    cell: ({ row }) => {
      return (
        <div className="font-medium text-slate-800">
          {row.original.giftCount || 0}
        </div>
      );
    },
  },
  {
    accessorKey: "giftDays",
    header: "赠送天数",
    cell: ({ row }) => {
      return (
        <div className="font-medium text-slate-800">
          {row.original.giftDays || 0}
        </div>
      );
    },
  },

  {
    accessorKey: "salesRepName",
    header: "销售员",
    cell: ({ row }) => {
      const salesPersonName = row.original.salesRepName || '未指定';
      return (
        <div className="text-slate-700">
          {salesPersonName}
        </div>
      );
    },
  },
  {
    accessorKey: "operatorName",
    header: "操作员",
    cell: ({ row }) => {
      const operatorName = row.original.operatorName || '未指定';
      return (
        <div className="text-slate-700">
          {operatorName}
        </div>
      );
    },
  },
  {
    accessorKey: "paymentStatus",
    header: "状态",
    cell: ({ row }) => {
      const status = String(row.original.studentProductPaymentStatus || "other").toLowerCase();
      const { label, color } = PURCHASE_RECORD_TYPE_MAP[status] || PURCHASE_RECORD_TYPE_MAP.default;
      return (
        <Badge className={cn("font-normal border py-0.5", color)}>
          {label}
        </Badge>
      );
    },
  },
  {
    accessorKey: "actions",
    header: "操作",
    cell: ({ row }) => {
      const product = row.original;
      const paymentStatus = product.studentProductPaymentStatus;
      const studentId = useAppSelector(state => state.currentStudent.studentId)

      return (
        <div className="flex items-center gap-1">
          {
           paymentStatus === "refunded" || !paymentStatus  ? (
              <Badge className="border py-0.5">已退款</Badge>
            ) : (
              <ActionProductRefund 
              studentId={studentId}
                studentProductId={product.studentProductId}
                remainingAmount={product.studentProductRemainingBalance || 0}
              />
            )
          }

        </div>
      )
    }
  }
];