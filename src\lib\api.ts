import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import { customToast } from '@/lib/toast';
// 创建axios实例
const api = axios.create({
    baseURL: 'http://127.0.0.1:3001',
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json'
    }
});

// 请求拦截器
api.interceptors.request.use(
    config => {
        const token = localStorage.getItem('token');
        console.log(token," token")
        if (token) {
            // 确保 headers 对象存在
            config.headers = config.headers || {};
            // 设置 Authorization 头
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);

// 存储正在重试的请求
const retryQueue: {
    config: AxiosRequestConfig;
    resolve: (value: any) => void;
    reject: (reason?: any) => void;
}[] = [];

// 响应拦截器
api.interceptors.response.use(
    (response: AxiosResponse) => {
        return response.data;
    },
    async (error: AxiosError) => {
        const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

        // 如果是401错误且不是刷新令牌的请求且没有重试过
        // if (
        //     error.response?.status === 401 &&
        //     originalRequest &&
        //     !originalRequest._retry &&
        //     originalRequest.url !== '/api/auth/refresh-token'
        // ) {
        //     originalRequest._retry = true;

        //     try {
        //         // 尝试刷新令牌
        //         const newToken = await refreshToken();

        //         if (newToken) {
        //             // 更新原始请求的令牌
        //             if (originalRequest.headers) {
        //                 originalRequest.headers.Authorization = `Bearer ${newToken}`;
        //             } else {
        //                 originalRequest.headers = { Authorization: `Bearer ${newToken}` };
        //             }

        //             // 重新发送原始请求
        //             return api(originalRequest);
        //         }
        //     } catch (refreshError) {
        //         console.error('令牌刷新失败:', refreshError);
        //         // 刷新令牌失败，清除令牌并跳转到登录页
        //         localStorage.removeItem('token');
        //         localStorage.removeItem('refresh_token');
        //         customToast.error('登录已过期，请重新登录');
        //         window.location.href = '/login';
        //         return Promise.reject(refreshError);
        //     }
        // }

        // 处理统一错误响应格式
        if (error.response?.data) {
            const errorData = error.response.data as any;

            // 检查是否是新的错误响应格式
            if (errorData.success === false && errorData.error) {
                const { message, code, status } = errorData.error;

                switch (status) {
                    case 401:
                        // 对于401错误，不在这里处理，让baseQueryWithReauth处理token刷新
                        // 只有在特定情况下才显示错误提示
                        if (error.config?.url?.includes('/api/auth/refresh-token')) {
                            // 如果是刷新token的请求失败，才显示错误提示
                            customToast.error(message || '登录已过期', '请重新登录');
                            // 清除所有相关的本地存储
                            localStorage.removeItem('userInfo');
                            localStorage.removeItem('user');
                            localStorage.removeItem('token');
                            localStorage.removeItem('refresh_token');
                            localStorage.removeItem('refreshToken');
                            localStorage.removeItem('persist:root');
                            // 延迟重定向，给用户时间看到提示
                            if (window.location.pathname !== '/login') {
                                setTimeout(() => {
                                    window.location.href = '/login';
                                }, 1500);
                            }
                        }
                        break;
                    case 403:
                        customToast.error(message || '没有权限访问该资源');
                        break;
                    case 404:
                        customToast.error(message || '请求的资源不存在');
                        break;
                    case 409:
                        customToast.error(message || '资源冲突');
                        break;
                    case 500:
                        customToast.error(message || '服务器错误');
                        break;
                    default:
                        customToast.error(message || '请求失败');
                }

                // 创建一个新的错误对象，包含错误信息
                const enhancedError = new Error(message);
                // 添加原始错误信息
                Object.assign(enhancedError, errorData.error);

                return Promise.reject(enhancedError);
            }
        } else {
            customToast.error('网络错误，请检查您的网络连接');
            return Promise.reject(new Error('网络错误，请检查您的网络连接'));
        }
    }
);

export default api;
