'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { cn } from '@/lib/utils';
import { ChevronDownIcon, ChevronLeftIcon, ChevronRightIcon, ChevronUpIcon } from 'lucide-react';

import { type MenuItem } from './navItems';
import OptimizedIcon from './icons/optimized-icon';
import { useAppSelector } from '@/stores/hooks';

interface SidebarProps {
  menuItems?: MenuItem[];
}

const Sidebar: React.FC<SidebarProps> = () => {

  const [isCollapsed, setIsCollapsed] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const pathname = usePathname();
  const menuItems = useAppSelector(state => state.userMenus.menus) || [];

  const cachedMenuItems = useMemo(() => menuItems, [menuItems]);

  useEffect(() => {
    // 递归查找所有父菜单项
    const findParentIds = (items: MenuItem[], targetPath: string): string[] => {
      const result: string[] = [];
      
      const search = (menuItems: MenuItem[], path: string[], currentPath: string) => {
        for (const item of menuItems) {
          if (item.path === targetPath) {
            // 找到目标项，保存路径
            result.push(...path);
            return true;
          }
          
          if (item.children?.length) {
            const found = search(item.children, [...path, item.id], item.path || '');
            if (found) return true;
          }
        }
        return false;
      };
      
      search(items, [], '');
      return result;
    };
    
    // 找到当前路径的所有父菜单ID
    const parentIds = findParentIds(cachedMenuItems, pathname);
    
    if (parentIds.length > 0) {
      setExpandedItems(prev => {
        const uniqueIds = new Set([...prev, ...parentIds]);
        return Array.from(uniqueIds);
      });
    }
  }, [pathname, cachedMenuItems]);

  const toggleExpand = useCallback((itemId: string) => {
    setExpandedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  }, []);

  const isActive = useCallback((href?: string) => {
    if (!href) return false;
    if (pathname === href) return true;
    return pathname.startsWith(href + '/');
  }, [pathname]);

  const renderMenuItem = useCallback((item: MenuItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.id);
    const active = isActive(item.path);

    const itemContent = (
      <div className="flex items-center flex-1 min-w-0">
        <div className={cn(
          "flex-shrink-0 w-5 h-5",
          active && "text-blue-600",
          isCollapsed && "mx-auto"
        )}>
          <OptimizedIcon
            name={item.icon}
            className={cn(
              "h-5 w-5",
              active && "text-blue-600"
            )}
            />
        </div>
        {!isCollapsed && (
          <span className={cn(
            "ml-3 truncate",
            active && "font-medium text-blue-600"
          )}>
            {item.name}
          </span>
        )}
      </div>
    );

    return (
      <div key={item.id} className={cn(
        "w-full",
        level > 0 && "pl-4"
      )}>
        {item.path ? (
          <Link
            href={item.path}
            className={cn(
              "flex items-center w-full p-2 rounded-lg transition-colors duration-200",
              "hover:bg-gray-100",
              active ? "bg-blue-50 text-blue-600" : "text-gray-700 hover:text-gray-900",
              isCollapsed && "justify-center"
            )}
          >
            {itemContent}
          </Link>
        ) : (
          <button
            onClick={() => toggleExpand(item.id)}
            className={cn(
              "flex items-center w-full p-2 rounded-lg transition-colors duration-200",
              "hover:bg-gray-100 text-gray-700 hover:text-gray-900",
              isCollapsed && "justify-center",
              isExpanded && "bg-gray-50"
            )}
          >
            <div className="flex items-center justify-between w-full">
              {itemContent}
              {!isCollapsed && hasChildren && (
                <div className="flex-shrink-0 ml-2">
                  {isExpanded ? (
                    <ChevronUpIcon className="w-4 h-4" />
                  ) : (
                    <ChevronDownIcon className="w-4 h-4" />
                  )}
                </div>
              )}
            </div>
          </button>
        )}

        {hasChildren && isExpanded && !isCollapsed && (
          <div className="mt-1 space-y-1 animate-slideDown">
            {item.children?.map(child => renderMenuItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  }, [expandedItems, isActive, isCollapsed, toggleExpand]);

  return (
    <nav
      className={cn(
        'bg-white shadow-xl transition-all duration-300 ease-in-out border-r border-gray-200 relative group',
        isCollapsed ? 'w-16' : 'w-64'
      )}
    >
      <div
        className={cn(
          'sticky top-0 flex flex-col h-full ',
          isCollapsed ? 'py-2 px-1 pt-16' : 'p-4 pt-16'
        )}
      >

        {/* Menu Items */}
        <div className="flex-1 space-y-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent">
          {cachedMenuItems.map(item => renderMenuItem(item))}
        </div>

        {/* Collapse Button */}
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className={cn(
            'absolute top-1/2 -right-3 w-6 h-6 bg-white border border-gray-200 rounded-full',
            'flex items-center justify-center text-gray-500 hover:text-gray-700',
            'transform -translate-y-1/2 shadow-sm hover:shadow transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
          )}
        >
          {isCollapsed ? (
            <ChevronRightIcon className="w-4 h-4" />
          ) : (
            <ChevronLeftIcon className="w-4 h-4" />
          )}
        </button>
      </div>
    </nav>
  );
};

export default React.memo(Sidebar);